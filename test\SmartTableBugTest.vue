<!-- SmartTable Bug测试用例 -->
<template>
  <div class="bug-test-container">
    <h2>SmartTable Cell-Click Bug 测试</h2>
    
    <!-- 控制按钮 -->
    <div class="controls">
      <el-button @click="toggleEditable" type="primary">
        {{ testColumn.editable ? '禁用编辑' : '启用编辑' }}
      </el-button>
      <el-button @click="toggleClickable" type="success">
        {{ testColumn.clickable ? '禁用点击' : '启用点击' }}
      </el-button>
      <el-button @click="resetTest" type="warning">重置测试</el-button>
    </div>

    <!-- 当前状态显示 -->
    <div class="status">
      <p><strong>当前列配置：</strong></p>
      <p>clickable: {{ testColumn.clickable }}</p>
      <p>editable: {{ testColumn.editable }}</p>
      <p><strong>最后一次点击事件：</strong> {{ lastClickEvent || '无' }}</p>
    </div>

    <!-- 测试表格 -->
    <OgwTable
      :columns="columns"
      :data="tableData"
      @cell-click="handleCellClick"
      @cell-change="handleCellChange"
    />

    <!-- 测试步骤说明 -->
    <div class="test-steps">
      <h3>测试步骤：</h3>
      <ol>
        <li>初始状态：测试列为 clickable: true, editable: false</li>
        <li>点击"测试值1"单元格，应该触发cell-click事件</li>
        <li>点击"启用编辑"按钮，将editable设置为true</li>
        <li>点击任意单元格进入编辑模式，然后失去焦点</li>
        <li>再次点击"测试值1"单元格，检查是否仍能触发cell-click事件</li>
      </ol>
      <p><strong>预期结果：</strong>第5步应该能正常触发cell-click事件</p>
    </div>
  </div>
</template>

<script>
import OgwTable from '@/components/comTable/OgwTable.vue';

export default {
  name: 'SmartTableBugTest',
  components: {
    OgwTable,
  },
  data() {
    return {
      lastClickEvent: null,
      testColumn: {
        label: '测试列',
        prop: 'testValue',
        clickable: true,
        editable: false,
      },
      dropdownColumn: {
        label: '下拉测试',
        prop: 'dropdownValue',
        editable: true,
        options: [
          { label: '选项1', value: 'option1' },
          { label: '选项2', value: 'option2' },
          { label: '选项3', value: 'option3' },
        ],
      },
      tableData: [
        { id: 1, name: '行1', testValue: '测试值1', dropdownValue: 'option1' },
        { id: 2, name: '行2', testValue: '测试值2', dropdownValue: 'option2' },
        { id: 3, name: '行3', testValue: '测试值3', dropdownValue: 'option3' },
      ],
    };
  },
  computed: {
    columns() {
      return [
        { label: 'ID', prop: 'id', width: 80 },
        { label: '名称', prop: 'name', width: 120 },
        // 使用响应式的测试列配置
        { ...this.testColumn },
        // 下拉框测试列
        { ...this.dropdownColumn },
      ];
    },
  },
  methods: {
    handleCellClick({ row, prop, index, value }) {
      const timestamp = new Date().toLocaleTimeString();
      this.lastClickEvent = `${timestamp} - 点击了第${index + 1}行的${prop}列，值为：${value}`;
      console.log('Cell clicked:', { row, prop, index, value });
      
      // 显示成功提示
      this.$message({
        message: `成功触发cell-click事件！列：${prop}，值：${value}`,
        type: 'success',
        duration: 3000,
      });
    },
    
    handleCellChange({ row, prop, index, value }) {
      console.log('Cell changed:', { row, prop, index, value });
      this.$message({
        message: `单元格值已更改：${prop} = ${value}`,
        type: 'info',
        duration: 2000,
      });
    },
    
    toggleEditable() {
      // 动态切换editable属性
      this.testColumn.editable = !this.testColumn.editable;
      
      this.$message({
        message: `测试列编辑模式已${this.testColumn.editable ? '启用' : '禁用'}`,
        type: 'info',
        duration: 2000,
      });
    },
    
    resetTest() {
      // 重置测试状态
      this.testColumn.clickable = true;
      this.testColumn.editable = false;
      this.lastClickEvent = null;
      
      this.$message({
        message: '测试状态已重置',
        type: 'success',
        duration: 2000,
      });
    },
  },
};
</script>

<style scoped>
.bug-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.controls {
  margin: 20px 0;
}

.controls .el-button {
  margin-right: 10px;
}

.status {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin: 20px 0;
}

.status p {
  margin: 5px 0;
}

.test-steps {
  margin-top: 30px;
  padding: 20px;
  background: #fff9e6;
  border-left: 4px solid #e6a23c;
  border-radius: 4px;
}

.test-steps h3 {
  margin-top: 0;
  color: #e6a23c;
}

.test-steps ol {
  margin: 15px 0;
  padding-left: 20px;
}

.test-steps li {
  margin: 8px 0;
  line-height: 1.5;
}
</style>
