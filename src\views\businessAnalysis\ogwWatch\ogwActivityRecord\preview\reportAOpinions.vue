<template>
  <div class="reportAOpinions">
    <div class="content">
      <el-card class="upload-card">
        <div slot="header" class="btns">
          <el-upload
            class="upload-demo"
            :action="`${prefix}/attachment/uploadFile`"
            :headers="uploadHeaders"
            multiple
            :limit="3"
            :on-exceed="handleExceed"
            :on-success="handleSuccess"
            :file-list="fileList"
            :show-file-list="false"
          >
            <el-button type="primary" class="confirm-btn" v-if="id"
              >上传</el-button
            >
          </el-upload>
          <el-button type="primary" class="confirm-btn" @click="handleCancle"
            >返回</el-button
          >
          <el-button
            type="primary"
            class="confirm-btn"
            @click="handleSubmit"
            v-if="id"
            >确定</el-button
          >
        </div>
        <div class="file-list">
          <template v-if="files.length > 0">
            <div
              class="file-item"
              :class="{ 'file-item-selected': currentFileId === file.fileId }"
              v-for="(file, index) in files"
              :key="index"
              @click="handlePreview(file)"
            >
              <div class="file-icon">
                <div class="icon-placeholder" :class="getFileTypeClass(file.originalName)">
                  <span class="file-icon-text">{{ getFileIcon(file.originalName) }}</span>
                </div>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.originalName }}</div>
                <div class="file-meta">
                  <span>文件大小：{{ file.size }}</span>
                  <span>上传人：{{ file.createBy }}</span>
                  <span>上传时间：{{ file.createTime }}</span>
                </div>
              </div>
              <div class="file-actions">
                <div
                  class="action-btn download-btn"
                  @click="handleDownload(file)"
                >下载</div>
                <div
                  class="action-btn preview-btn"
                  @click="handlePreview(file)"
                >预览</div>
                <div
                  class="action-btn delete-btn"
                  @click="handleDelete(file, index)"
                >删除</div>
              </div>
            </div>
          </template>
          <div v-else class="empty-state">
            <div class="empty-icon">📁</div>
            <div class="empty-text">暂无文件</div>
            <div class="empty-desc">您可以点击上方的上传按钮添加文件</div>
          </div>
        </div>
      </el-card>
    </div>
    <div class="preview-box">
      <pdf-viewer
        v-if="fileUrl && fileType === 'pdf'"
        :pdf-url="fileUrl"
      ></pdf-viewer>
      <word-viewer
        v-if="fileUrl && (fileType === 'docx' || fileType === 'doc')"
        :file-url="fileUrl"
        :file-type="fileType"
      ></word-viewer>
    </div>
  </div>
</template>
<script>
import PdfViewer from "@/components/common/pdfView.vue";
import WordViewer from "@/components/common/wordView.vue";
import {
  getFileList,
  downloadFileById,
  previewFileById,
} from "@/api/file/index.js";
import { saveFilterChemicals } from "@/api/ogwActiveRecord/chemicalsServe.js";
import { downFileUtil } from "@/utils/file.js";
export default {
  name: "reportAOpinions",
  components: {
    PdfViewer,
    WordViewer,
  },
  activated() {
    this.id = this.$route.params.id || null;
    const fileIds = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    this.files = [];
    this.fileUrl = null;
    this.currentFileId = null; // 重置选中状态
    if (fileIds) {
      this.getFile(fileIds);
    }
    this.initUploadHeaders();
  },
  mounted() {
    this.id = this.$route.params.id || null;
    const fileIds = this.$route.params.fileIds;
    this.formType = this.$route.params.type;
    if (fileIds) {
      this.getFile(fileIds);
    }
    // 获取.env.production中的VUE_APP_PREFIX
    this.prefix = process.env.VUE_APP_PREFIX;
    this.token = localStorage.getItem("access_token");
    this.initUploadHeaders();
  },
  data() {
    return {
      fileType: "",
      fileUrl: "",
      id: "",
      fileList: [],
      files: [],
      form: [],
      formType: null,
      prefix: "",
      token: "",
      uploadHeaders: {},
      currentFileId: null, // 当前选中的文件ID
    };
  },
  methods: {
    /**
     * 初始化上传请求头，添加身份验证token
     */
    initUploadHeaders() {
      const access_token = localStorage.getItem("access_token");
      this.uploadHeaders = {
        client_id: process.env.VUE_APP_ID,
        client_secret: process.env.VUE_APP_ID,
      };
      if (access_token) {
        this.uploadHeaders.Authorization = access_token;
      }
    },
    /**
     * 根据文件名获取文件图标
     */
    getFileIcon(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      const iconMap = {
        'pdf': '📄',
        'doc': '📝',
        'docx': '📝',
        'xls': '📊',
        'xlsx': '📊',
        'ppt': '📋',
        'pptx': '📋',
        'txt': '📄',
        'jpg': '🖼️',
        'jpeg': '🖼️',
        'png': '🖼️',
        'gif': '🖼️',
        'zip': '📦',
        'rar': '📦',
        '7z': '📦'
      };
      return iconMap[ext] || '📄';
    },
    /**
     * 根据文件名获取文件类型样式类
     */
    getFileTypeClass(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        'pdf': 'file-type-pdf',
        'doc': 'file-type-word',
        'docx': 'file-type-word',
        'xls': 'file-type-excel',
        'xlsx': 'file-type-excel',
        'ppt': 'file-type-ppt',
        'pptx': 'file-type-ppt',
        'txt': 'file-type-text',
        'jpg': 'file-type-image',
        'jpeg': 'file-type-image',
        'png': 'file-type-image',
        'gif': 'file-type-image',
        'zip': 'file-type-archive',
        'rar': 'file-type-archive',
        '7z': 'file-type-archive'
      };
      return typeMap[ext] || 'file-type-default';
    },
    handleSubmit() {
      const data = {
        id: this.id,
        reportFileId: this.form.join(","),
      };
      this.saveTableRow(data);
    },
    handleCancle() {
      this.$router.back();
    },
    handleExceed(files, fileList) {
      this.$message.warning("最多只能上传3个文件");
    },
    handleSuccess(response, file, fileList) {
      this.$message.success("上传成功");
      this.files.push(response.data);
      this.form.push(response.data.fileId);
    },
    async handleDownload(file) {
      const res = await downloadFileById(file.fileId);
      if (res) {
        downFileUtil(res, file.originalName);
      }
    },
    async handlePreview(file) {
      this.currentFileId = file.fileId; // 设置当前选中的文件ID
      this.fileType = file.originalName.split(".").pop();
      const id = file.fileId;
      const res = await previewFileById(id);
      if (res.code === 200) {
        this.fileUrl = res.data;
      }
    },
    handleDelete(file, index) {
      console.log("删除文件:", file.name);
      this.$confirm("确认删除该文件吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.files.splice(index, 1);
          this.$message.success("删除成功");
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    async getFile(id) {
      const res = await getFileList(id);
      if (res.code === 200) {
        this.files = res.data;
        // 如果文件列表不为空，自动打开第一个文件
        if (this.files.length > 0) {
          await this.handlePreview(this.files[0]);
        }
      }
    },
    async saveTableRow(data) {
      let res = null;
      switch (this.formType) {
        case "filter":
          data.type = 1;
          res = await saveFilterChemicals(data);
          break;
        case "evaluate":
          data.type = 2;
          res = await saveFilterChemicals(data);
          break;
      }
      if (res.code === 200) {
        this.$message.success("保存成功");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.reportAOpinions {
  background: #fff;
  display: flex;
  .preview-box {
    flex: 1;
    height: 100%;
    border-left: 1px solid #ccc;
  }
  .content {
    height: auto;
    flex: 1;
    // padding: 20px;
    .upload-card {
      max-width: 800px;
      height: 100%;
      margin: 0 auto;
      .btns {
        display: flex;
      }
      .confirm-btn {
        margin-left: 12px;
      }

      .device-section {
        padding: 0 20px;
      }

      .el-form-item {
        margin-bottom: 22px;
      }
    }
  }
}

/* 深色主题适配 - reportAOpinions 主容器 */
html[data-theme='dark'] {
  .reportAOpinions {
    background-color: #0c1324;
    color: #ffffff;

    .preview-box {
      border-left: 1px solid #4F98F6;
      background-color: #1A2E52;
    }

    .content {
      background-color: #0c1324;

      .upload-card {
        background-color: #1A2E52;
        border: 1px solid #4F98F6;

        ::v-deep .el-card__header {
          background-color: #162549;
          border-bottom: 1px solid #4F98F6;
          color: #ffffff;
        }

        ::v-deep .el-card__body {
          background-color: #1A2E52;
          color: #ffffff;
        }
      }
    }
  }
}

/* 浅色主题适配 - reportAOpinions 主容器 */
[data-theme="tint"],
[data-theme="defaule"] {
  .reportAOpinions {
    background-color: #ffffff;
    color: #2E3641;

    .preview-box {
      border-left: 1px solid #EAEFF5;
      background-color: #ffffff;
    }

    .content {
      background-color: #ffffff;

      .upload-card {
        background-color: #ffffff;
        border: 1px solid #EAEFF5;

        ::v-deep .el-card__header {
          background-color: #F8FAFC;
          border-bottom: 1px solid #EAEFF5;
          color: #2E3641;
        }

        ::v-deep .el-card__body {
          background-color: #ffffff;
          color: #2E3641;
        }
      }
    }
  }
}

/* 文件列表容器 */
.file-list {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 文件列表项 */
.file-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 文件选中状态样式 */
.file-item-selected {
  background-color: #e6f7ff !important;
  border-left: 4px solid #409eff !important;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2) !important;
}

.file-item-selected:hover {
  background-color: #d6f3ff !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;
}

/* 文件图标区域 */
.file-icon {
  margin-right: 16px;
  position: relative;
}

.icon-placeholder {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.file-icon-text {
  font-size: 20px;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 不同文件类型的图标样式 */
.file-type-pdf {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.file-type-word {
  background: linear-gradient(135deg, #4dabf7 0%, #339af0 100%);
  box-shadow: 0 2px 8px rgba(77, 171, 247, 0.3);
}

.file-type-excel {
  background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
  box-shadow: 0 2px 8px rgba(81, 207, 102, 0.3);
}

.file-type-ppt {
  background: linear-gradient(135deg, #ff8cc8 0%, #ff6b9d 100%);
  box-shadow: 0 2px 8px rgba(255, 140, 200, 0.3);
}

.file-type-image {
  background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
  box-shadow: 0 2px 8px rgba(255, 212, 59, 0.3);
}

.file-type-archive {
  background: linear-gradient(135deg, #9775fa 0%, #845ef7 100%);
  box-shadow: 0 2px 8px rgba(151, 117, 250, 0.3);
}

.file-type-text {
  background: linear-gradient(135deg, #74c0fc 0%, #339af0 100%);
  box-shadow: 0 2px 8px rgba(116, 192, 252, 0.3);
}

.file-type-default {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 2px dashed #e2e8f0;
  margin: 20px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #94a3b8;
  line-height: 1.5;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px;
  }

  .file-icon {
    margin-right: 0;
    margin-bottom: 12px;
    align-self: center;
  }

  .file-info {
    width: 100%;
    text-align: center;
    margin-bottom: 16px;
  }

  .file-actions {
    margin-left: 0;
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-btn {
    flex: 1;
    min-width: 80px;
  }

  .file-meta {
    justify-content: center;
    flex-direction: column;
    gap: 4px;
  }

  .file-meta span {
    padding-left: 0;
  }

  .file-meta span::before {
    display: none;
  }
}

/* 文件信息区域 */
.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 15px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1.4;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  font-size: 12px;
  color: #8492a6;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
}

.file-meta span {
  position: relative;
  padding-left: 16px;
}

.file-meta span::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background-color: #d1d5db;
  border-radius: 50%;
}

.file-meta span:first-child {
  padding-left: 0;
}

.file-meta span:first-child::before {
  display: none;
}

/* 操作按钮区域 */
.file-actions {
  flex-shrink: 0;
  margin-left: 16px;
  display: flex;
  gap: 8px;
}

.action-btn {
  min-width: 64px;
  height: 32px;
  padding: 0 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

/* 下载按钮样式 */
.download-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-color: #4facfe;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.download-btn:hover {
  background: linear-gradient(135deg, #3b8bfe 0%, #00d4fe 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(79, 172, 254, 0.4);
}

.download-btn:active {
  transform: translateY(0);
}

/* 预览按钮样式 */
.preview-btn {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c3e50;
  border-color: #a8edea;
  box-shadow: 0 2px 8px rgba(168, 237, 234, 0.3);
}

.preview-btn:hover {
  background: linear-gradient(135deg, #96e6e1 0%, #fcc9d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(168, 237, 234, 0.4);
}

.preview-btn:active {
  transform: translateY(0);
}

/* 删除按钮样式 */
.delete-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #e53e3e;
  border-color: #ff9a9e;
  box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3);
}

.delete-btn:hover {
  background: linear-gradient(135deg, #ff8a8e 0%, #fdbfdf 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(255, 154, 158, 0.4);
}

.delete-btn:active {
  transform: translateY(0);
}

/* 深色主题样式 */
html[data-theme='dark'] {
  .file-list {
    border-color: #4F98F6;
    background: #1A2E52;
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.2);
  }

  .file-item {
    border-bottom-color: rgba(79, 152, 246, 0.3);
  }

  .file-item:hover {
    background-color: rgba(79, 152, 246, 0.1);
    box-shadow: 0 4px 12px rgba(79, 152, 246, 0.2);
  }

  /* 深色主题下的文件选中状态 */
  .file-item-selected {
    background-color: rgba(79, 152, 246, 0.2) !important;
    border-left: 4px solid #4F98F6 !important;
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.4) !important;
  }

  .file-item-selected:hover {
    background-color: rgba(79, 152, 246, 0.3) !important;
    box-shadow: 0 4px 12px rgba(79, 152, 246, 0.5) !important;
  }

  .icon-placeholder {
    background: linear-gradient(135deg, #4F98F6 0%, #6BA4F4 100%);
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.4);
  }

  .file-name {
    color: #ffffff;
  }

  .file-meta {
    color: #B3D3E5;
  }

  .file-meta span::before {
    background-color: #6493D4;
  }

  .download-btn {
    background: linear-gradient(135deg, #4F98F6 0%, #6BA4F4 100%);
    border-color: #4F98F6;
    box-shadow: 0 2px 8px rgba(79, 152, 246, 0.4);
  }

  .download-btn:hover {
    background: linear-gradient(135deg, #3578F6 0%, #5B94F4 100%);
    box-shadow: 0 4px 16px rgba(79, 152, 246, 0.5);
  }

  .preview-btn {
    background: linear-gradient(135deg, #639EF6 0%, #7BAAF6 100%);
    color: #ffffff;
    border-color: #639EF6;
    box-shadow: 0 2px 8px rgba(99, 158, 246, 0.4);
  }

  .preview-btn:hover {
    background: linear-gradient(135deg, #5B94F4 0%, #73A4F6 100%);
    box-shadow: 0 4px 16px rgba(99, 158, 246, 0.5);
  }

  .delete-btn {
    background: linear-gradient(135deg, #F74A4D 0%, #FF6B6E 100%);
    color: #ffffff;
    border-color: #F74A4D;
    box-shadow: 0 2px 8px rgba(247, 74, 77, 0.4);
  }

  .delete-btn:hover {
    background: linear-gradient(135deg, #E53E3E 0%, #F56565 100%);
    box-shadow: 0 4px 16px rgba(247, 74, 77, 0.5);
  }

  .empty-state {
    background: linear-gradient(135deg, #1A2E52 0%, #2A3F6B 100%);
    border-color: rgba(79, 152, 246, 0.3);
  }

  .empty-text {
    color: #ffffff;
  }

  .empty-desc {
    color: #B3D3E5;
  }
}

/* 浅色主题（tint）样式增强 */
[data-theme="tint"],
[data-theme="defaule"] {
  .file-list {
    border-color: #EAEFF5;
    background: #ffffff;
  }

  .file-item:hover {
    background-color: #F8FAFC;
  }

  /* 浅色主题下的文件选中状态 */
  .file-item-selected {
    background-color: #E6F7FF !important;
    border-left: 4px solid #409EFF !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15) !important;
  }

  .file-item-selected:hover {
    background-color: #D6F3FF !important;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25) !important;
  }

  .file-name {
    color: #2E3641;
  }

  .file-meta {
    color: #97A0AA;
  }

  .icon-placeholder {
    background: linear-gradient(135deg, #4EA0FD 0%, #639EF6 100%);
  }

  /* 浅色主题下的简洁按钮样式 */
  .download-btn {
    background: #409eff;
    color: white;
    border-color: #409eff;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  }

  .download-btn:hover {
    background: #337ecc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
  }

  .preview-btn {
    background: #67c23a;
    color: white;
    border-color: #67c23a;
    box-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
  }

  .preview-btn:hover {
    background: #529b2e;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(103, 194, 58, 0.3);
  }

  .delete-btn {
    background: #f56c6c;
    color: white;
    border-color: #f56c6c;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
  }

  .delete-btn:hover {
    background: #dd6161;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(245, 108, 108, 0.3);
  }
}
</style>
