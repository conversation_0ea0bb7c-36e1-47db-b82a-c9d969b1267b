# 药剂配置级联下拉框调试信息

## 问题分析

### 原始问题
1. 第一行药剂型号单元格点击正常，能显示对应选项
2. 第二行药剂型号单元格点击时，显示的仍是第一行的选项
3. 第二次点击时`handleCellClick`方法没有触发

### 根本原因
1. **全局options问题**: `this.columns[2].options`是全局的，所有行共享同一个options数组
2. **事件处理冲突**: 当列既是`editable`又是`clickable`时，事件处理存在冲突
3. **数据匹配不一致**: 不同方法中使用了不同的字段匹配药剂名称

## 解决方案

### 1. 事件处理机制优化
- 添加`@enter-edit="handleEnterEdit"`事件监听
- 在进入编辑模式时动态更新options
- 修改OgwTable组件，确保向外发出enter-edit事件

### 2. 行级别选项更新
- 创建`updateChemicalTypeOptionsForRow(row, rowIndex)`方法
- 每次进入编辑模式时，根据当前行的药剂名称动态更新选项
- 使用`this.$set(this.columns[2], 'options', options)`确保响应式更新

### 3. 数据匹配统一
- 统一使用`item.value === row.chemicalName`进行匹配
- 确保药剂名称存储的是`value`值（prdCode）

## 修改的文件

### 1. medicineConfig/index.vue
- 添加`@enter-edit="handleEnterEdit"`事件监听
- 新增`handleEnterEdit`方法
- 重构`updateChemicalTypeOptionsForRow`方法
- 修复`handleChemicalNameChange`方法
- 统一数据匹配逻辑

### 2. OgwTable.vue
- 修改`enterEdit`方法，添加`this.$emit("enter-edit", { index, prop })`

## 测试场景

### 场景1: 新增多行数据
1. 点击"新增"按钮添加第一行
2. 选择第一行的药剂名称
3. 点击第一行的药剂型号，验证选项正确
4. 点击"新增"按钮添加第二行
5. 选择第二行的药剂名称（与第一行不同）
6. 点击第二行的药剂型号，验证选项与第一行不同

### 场景2: 药剂名称变化联动
1. 在某行选择药剂名称A
2. 验证药剂型号选项更新为A对应的选项
3. 修改该行的药剂名称为B
4. 验证药剂型号被清空，选项更新为B对应的选项

### 场景3: 直接点击药剂型号
1. 在未选择药剂名称的行点击药剂型号
2. 验证提示"请先选择药剂名称"
3. 在已选择药剂名称的行点击药剂型号
4. 验证显示对应的型号选项

## 调试日志

在浏览器控制台中可以看到以下日志：
- "单元格点击事件:" - 点击单元格时
- "进入编辑模式:" - 进入编辑模式时
- "更新行级药剂型号选项:" - 更新选项时
- "药剂名称变化:" - 药剂名称变化时
- "成功更新药剂型号选项:" - 选项更新成功时

## 数据结构

```javascript
// 药剂名称选项结构
chemicalNameOptions: [
  {
    label: "聚合氯化铝",    // 显示名称
    value: "PAC001",       // 存储值（prdCode）
    child: [               // 对应的型号选项
      { id: "1", chemicalType: "PAC-01" },
      { id: "2", chemicalType: "PAC-02" }
    ]
  }
]

// 表格行数据结构
tableData: [
  {
    chemicalName: "PAC001",    // 存储的是value值
    chemicalType: "PAC-01",    // 存储的是型号名称
    // ... 其他字段
  }
]
```
